# تطبيق الدوريات الأوروبية

تطبيق أندرويد لعرض نتائج الدوريات الأوروبية باللغة العربية مع دعم RTL.

## المميزات

- عرض قائمة الدوريات الأوروبية الرئيسية
- جدول ترتيب الفرق في كل دوري
- واجهة مستخدم باللغة العربية مع دعم RTL
- تصميم Material Design
- تحديث البيانات عبر السحب للأسفل

## الدوريات المدعومة

- الدوري الإنجليزي الممتاز (Premier League)
- الدوري الإسباني (La Liga)
- الدوري الألماني (Bundesliga)
- الدوري الإيطالي (Serie A)
- الدوري الفرنسي (Ligue 1)
- دوري أبطال أوروبا (Champions League)
- الدوري الأوروبي (Europa League)

## متطلبات التشغيل

- Android 7.0 (API level 24) أو أحدث
- اتصال بالإنترنت

## إعداد المشروع

1. احصل على API key مجاني من [Football-Data.org](https://www.football-data.org/client/register)
2. افتح ملف 
3. استبدل `YOUR_API_KEY_HERE` بـ API key الخاص بك`app/src/main/java/com/europeanleagues/app/data/api/ApiClient.kt`
4. قم ببناء وتشغيل التطبيق

## البنية التقنية

- **اللغة**: Kotlin
- **Architecture**: MVVM
- **UI**: Material Design Components
- **Networking**: Retrofit + OkHttp
- **Image Loading**: Glide
- **Data Binding**: Android Data Binding

## الملفات الرئيسية

### نماذج البيانات
- `League.kt` - نموذج بيانات الدوري
- `Team.kt` - نموذج بيانات الفريق
- `Match.kt` - نموذج بيانات المباراة
- `Standing.kt` - نموذج بيانات جدول الترتيب

### خدمات API
- `FootballApiService.kt` - واجهة Retrofit للـ API
- `ApiClient.kt` - إعداد Retrofit client

### واجهة المستخدم
- `MainActivity.kt` - النشاط الرئيسي
- `LeaguesFragment.kt` - شاشة قائمة الدوريات
- `StandingsFragment.kt` - شاشة جدول الترتيب

## التطوير المستقبلي

- إضافة شاشة تفاصيل المباريات
- إضافة شاشة النتائج الأخيرة
- إضافة الإشعارات للمباريات المهمة
- إضافة المفضلة للفرق
- إضافة إحصائيات اللاعبين

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والشخصي.
