package com.europeanleagues.app.data.api

import com.europeanleagues.app.data.model.*
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Path
import retrofit2.http.Query

interface FootballApiService {
    
    @GET("competitions")
    suspend fun getCompetitions(): Response<CompetitionsResponse>
    
    @GET("competitions/{id}/standings")
    suspend fun getStandings(@Path("id") competitionId: Int): Response<StandingsResponse>
    
    @GET("competitions/{id}/matches")
    suspend fun getMatches(
        @Path("id") competitionId: Int,
        @Query("status") status: String? = null,
        @Query("matchday") matchday: Int? = null,
        @Query("dateFrom") dateFrom: String? = null,
        @Query("dateTo") dateTo: String? = null
    ): Response<MatchesResponse>
    
    @GET("matches/{id}")
    suspend fun getMatch(@Path("id") matchId: Int): Response<MatchResponse>
    
    @GET("teams/{id}")
    suspend fun getTeam(@Path("id") teamId: Int): Response<TeamResponse>
}

data class CompetitionsResponse(
    val count: Int,
    val filters: Map<String, Any>,
    val competitions: List<League>
)

data class StandingsResponse(
    val filters: Map<String, Any>,
    val area: Area,
    val competition: League,
    val season: Season,
    val standings: List<Standing>
)

data class MatchesResponse(
    val filters: Map<String, Any>,
    val resultSet: ResultSet,
    val competition: League,
    val matches: List<Match>
)

data class MatchResponse(
    val area: Area,
    val competition: League,
    val season: Season,
    val id: Int,
    val utcDate: String,
    val status: String,
    val matchday: Int?,
    val stage: String?,
    val group: String?,
    val lastUpdated: String,
    val homeTeam: Team,
    val awayTeam: Team,
    val score: Score?,
    val goals: List<Goal>?,
    val bookings: List<Booking>?,
    val substitutions: List<Substitution>?,
    val odds: Odds?,
    val referees: List<Referee>?
)

data class TeamResponse(
    val area: Area,
    val id: Int,
    val name: String,
    val shortName: String?,
    val tla: String?,
    val crest: String?,
    val address: String?,
    val website: String?,
    val founded: Int?,
    val clubColors: String?,
    val venue: String?,
    val runningCompetitions: List<League>?,
    val coach: Coach?,
    val squad: List<Player>?,
    val staff: List<Staff>?,
    val lastUpdated: String?
)

data class Area(
    val id: Int,
    val name: String,
    val code: String,
    val flag: String?
)

data class ResultSet(
    val count: Int,
    val first: String,
    val last: String,
    val played: Int
)

data class Coach(
    val id: Int?,
    val firstName: String?,
    val lastName: String?,
    val name: String?,
    val dateOfBirth: String?,
    val nationality: String?,
    val contract: Contract?
)

data class Contract(
    val start: String?,
    val until: String?
)

data class Staff(
    val id: Int?,
    val firstName: String?,
    val lastName: String?,
    val name: String?,
    val dateOfBirth: String?,
    val nationality: String?
)
