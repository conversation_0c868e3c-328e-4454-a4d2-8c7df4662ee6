package com.europeanleagues.app.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class League(
    val id: Int,
    val name: String,
    val code: String,
    val emblem: String?,
    val plan: String,
    val currentSeason: Season?,
    val numberOfAvailableSeasons: Int,
    val lastUpdated: String
) : Parcelable

@Parcelize
data class Season(
    val id: Int,
    val startDate: String,
    val endDate: String,
    val currentMatchday: Int?,
    val winner: Team?
) : Parcelable
