package com.europeanleagues.app.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class Match(
    val id: Int,
    val utcDate: String,
    val status: String,
    val matchday: Int?,
    val stage: String?,
    val group: String?,
    val lastUpdated: String,
    val homeTeam: Team,
    val awayTeam: Team,
    val score: Score?,
    val goals: List<Goal>?,
    val bookings: List<Booking>?,
    val substitutions: List<Substitution>?,
    val odds: Odds?,
    val referees: List<Referee>?
) : Parcelable

@Parcelize
data class Score(
    val winner: String?,
    val duration: String,
    val fullTime: ScoreDetail?,
    val halfTime: ScoreDetail?,
    val extraTime: ScoreDetail?,
    val penalties: ScoreDetail?
) : Parcelable

@Parcelize
data class ScoreDetail(
    val home: Int?,
    val away: Int?
) : Parcelable

@Parcelize
data class Goal(
    val minute: Int,
    val injuryTime: Int?,
    val type: String,
    val team: Team,
    val scorer: Player,
    val assist: Player?
) : Parcelable

@Parcelize
data class Booking(
    val minute: Int,
    val team: Team,
    val player: Player,
    val card: String
) : Parcelable

@Parcelize
data class Substitution(
    val minute: Int,
    val team: Team,
    val playerOut: Player,
    val playerIn: Player
) : Parcelable

@Parcelize
data class Player(
    val id: Int,
    val name: String,
    val position: String?,
    val dateOfBirth: String?,
    val nationality: String?
) : Parcelable

@Parcelize
data class Odds(
    val msg: String?
) : Parcelable

@Parcelize
data class Referee(
    val id: Int,
    val name: String,
    val type: String,
    val nationality: String?
) : Parcelable
