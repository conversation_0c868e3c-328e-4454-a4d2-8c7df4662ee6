package com.europeanleagues.app.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class Standing(
    val stage: String,
    val type: String,
    val group: String?,
    val table: List<TableEntry>
) : Parcelable

@Parcelize
data class TableEntry(
    val position: Int,
    val team: Team,
    val playedGames: Int,
    val form: String?,
    val won: Int,
    val draw: Int,
    val lost: Int,
    val points: Int,
    val goalsFor: Int,
    val goalsAgainst: Int,
    val goalDifference: Int
) : Parcelable
