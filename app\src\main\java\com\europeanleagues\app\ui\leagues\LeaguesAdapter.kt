package com.europeanleagues.app.ui.leagues

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.europeanleagues.app.R
import com.europeanleagues.app.data.model.League
import com.europeanleagues.app.databinding.ItemLeagueBinding

class LeaguesAdapter(
    private val onLeagueClick: (League) -> Unit
) : ListAdapter<League, LeaguesAdapter.LeagueViewHolder>(LeagueDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LeagueViewHolder {
        val binding = ItemLeagueBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return LeagueViewHolder(binding)
    }

    override fun onBindViewHolder(holder: <PERSON><PERSON><PERSON>wHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class LeagueViewHolder(
        private val binding: ItemLeagueBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(league: League) {
            binding.apply {
                textLeagueName.text = getLeagueNameInArabic(league.name)
                
                league.currentSeason?.let { season ->
                    textLeagueSeason.text = "موسم ${season.startDate.substring(0, 4)}-${season.endDate.substring(0, 4)}"
                    season.currentMatchday?.let { matchday ->
                        textLeagueMatchday.text = "الجولة $matchday"
                    }
                }
                
                // Load league logo
                Glide.with(imageLeagueLogo.context)
                    .load(league.emblem)
                    .placeholder(R.drawable.ic_menu_leagues)
                    .error(R.drawable.ic_menu_leagues)
                    .into(imageLeagueLogo)
                
                root.setOnClickListener {
                    onLeagueClick(league)
                }
            }
        }
        
        private fun getLeagueNameInArabic(englishName: String): String {
            return when {
                englishName.contains("Premier League", ignoreCase = true) -> "الدوري الإنجليزي الممتاز"
                englishName.contains("La Liga", ignoreCase = true) -> "الدوري الإسباني"
                englishName.contains("Bundesliga", ignoreCase = true) -> "الدوري الألماني"
                englishName.contains("Serie A", ignoreCase = true) -> "الدوري الإيطالي"
                englishName.contains("Ligue 1", ignoreCase = true) -> "الدوري الفرنسي"
                englishName.contains("Champions League", ignoreCase = true) -> "دوري أبطال أوروبا"
                englishName.contains("Europa League", ignoreCase = true) -> "الدوري الأوروبي"
                else -> englishName
            }
        }
    }

    class LeagueDiffCallback : DiffUtil.ItemCallback<League>() {
        override fun areItemsTheSame(oldItem: League, newItem: League): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: League, newItem: League): Boolean {
            return oldItem == newItem
        }
    }
}
