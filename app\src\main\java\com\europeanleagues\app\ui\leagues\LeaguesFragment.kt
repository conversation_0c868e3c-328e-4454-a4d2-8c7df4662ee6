package com.europeanleagues.app.ui.leagues

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.europeanleagues.app.databinding.FragmentLeaguesBinding

class LeaguesFragment : Fragment() {

    private var _binding: FragmentLeaguesBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var viewModel: LeaguesViewModel
    private lateinit var adapter: LeaguesAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentLeaguesBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        viewModel = ViewModelProvider(this)[LeaguesViewModel::class.java]
        
        setupRecyclerView()
        setupSwipeRefresh()
        observeViewModel()
        
        viewModel.loadLeagues()
    }
    
    private fun setupRecyclerView() {
        adapter = LeaguesAdapter { league ->
            // Handle league click
            // Navigate to standings or matches for this league
        }
        
        binding.recyclerViewLeagues.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = <EMAIL>
        }
    }
    
    private fun setupSwipeRefresh() {
        binding.swipeRefresh.setOnRefreshListener {
            viewModel.loadLeagues()
        }
    }
    
    private fun observeViewModel() {
        viewModel.leagues.observe(viewLifecycleOwner) { leagues ->
            adapter.submitList(leagues)
        }
        
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.swipeRefresh.isRefreshing = isLoading
            binding.progressBar.visibility = if (isLoading && adapter.itemCount == 0) {
                View.VISIBLE
            } else {
                View.GONE
            }
        }
        
        viewModel.error.observe(viewLifecycleOwner) { error ->
            binding.textError.visibility = if (error != null) View.VISIBLE else View.GONE
            if (error != null) {
                binding.textError.text = error
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
