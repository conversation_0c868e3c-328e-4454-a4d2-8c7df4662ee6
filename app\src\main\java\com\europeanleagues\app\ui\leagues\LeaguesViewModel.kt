package com.europeanleagues.app.ui.leagues

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.europeanleagues.app.data.api.ApiClient
import com.europeanleagues.app.data.model.League
import kotlinx.coroutines.launch

class LeaguesViewModel : ViewModel() {
    
    private val _leagues = MutableLiveData<List<League>>()
    val leagues: LiveData<List<League>> = _leagues
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error
    
    private val apiService = ApiClient.footballApiService
    
    fun loadLeagues() {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            
            try {
                val response = apiService.getCompetitions()
                if (response.isSuccessful) {
                    val competitions = response.body()?.competitions ?: emptyList()
                    // Filter for major European leagues
                    val europeanLeagues = competitions.filter { league ->
                        league.id in listOf(
                            2021, // Premier League
                            2014, // La Liga
                            2002, // Bundesliga
                            2019, // Serie A
                            2015, // Ligue 1
                            2001, // Champions League
                            2018  // Europa League
                        )
                    }
                    _leagues.value = europeanLeagues
                } else {
                    _error.value = "خطأ في تحميل البيانات: ${response.code()}"
                }
            } catch (e: Exception) {
                _error.value = "خطأ في الاتصال بالإنترنت: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
}
