package com.europeanleagues.app.ui.standings

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.europeanleagues.app.R
import com.europeanleagues.app.data.model.TableEntry
import com.europeanleagues.app.databinding.ItemStandingBinding

class StandingsAdapter : ListAdapter<TableEntry, StandingsAdapter.StandingViewHolder>(StandingDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): StandingViewHolder {
        val binding = ItemStandingBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return StandingViewHolder(binding)
    }

    override fun onBindViewHolder(holder: StandingViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class StandingViewHolder(
        private val binding: ItemStandingBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(tableEntry: TableEntry) {
            binding.apply {
                textPosition.text = tableEntry.position.toString()
                textTeamName.text = tableEntry.team.name
                textPoints.text = tableEntry.points.toString()
                
                val goalDiff = tableEntry.goalDifference
                textGoalDifference.text = if (goalDiff >= 0) "+$goalDiff" else goalDiff.toString()
                
                textTeamStats.text = "لعب: ${tableEntry.playedGames} | " +
                        "فاز: ${tableEntry.won} | " +
                        "تعادل: ${tableEntry.draw} | " +
                        "خسر: ${tableEntry.lost}"
                
                // Load team logo
                Glide.with(imageTeamLogo.context)
                    .load(tableEntry.team.crest)
                    .placeholder(R.drawable.ic_menu_leagues)
                    .error(R.drawable.ic_menu_leagues)
                    .into(imageTeamLogo)
                
                // Set position background color based on position
                val context = textPosition.context
                val backgroundColor = when (tableEntry.position) {
                    in 1..4 -> context.getColor(R.color.football_green) // Champions League
                    in 5..6 -> context.getColor(R.color.football_orange) // Europa League
                    in 18..20 -> context.getColor(R.color.football_red) // Relegation
                    else -> context.getColor(R.color.dark_gray) // Mid-table
                }
                textPosition.setBackgroundColor(backgroundColor)
            }
        }
    }

    class StandingDiffCallback : DiffUtil.ItemCallback<TableEntry>() {
        override fun areItemsTheSame(oldItem: TableEntry, newItem: TableEntry): Boolean {
            return oldItem.team.id == newItem.team.id
        }

        override fun areContentsTheSame(oldItem: TableEntry, newItem: TableEntry): Boolean {
            return oldItem == newItem
        }
    }
}
