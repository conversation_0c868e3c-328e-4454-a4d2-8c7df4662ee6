package com.europeanleagues.app.ui.standings

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.AdapterView
import android.widget.ArrayAdapter
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.europeanleagues.app.R
import com.europeanleagues.app.data.model.League
import com.europeanleagues.app.databinding.FragmentStandingsBinding

class StandingsFragment : Fragment() {

    private var _binding: FragmentStandingsBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var viewModel: StandingsViewModel
    private lateinit var adapter: StandingsAdapter
    private lateinit var leagueSpinnerAdapter: ArrayAdapter<String>
    private val leagues = mutableListOf<League>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentStandingsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        viewModel = ViewModelProvider(this)[StandingsViewModel::class.java]
        
        setupRecyclerView()
        setupSpinner()
        setupSwipeRefresh()
        observeViewModel()
        
        viewModel.loadLeagues()
    }
    
    private fun setupRecyclerView() {
        adapter = StandingsAdapter()
        
        binding.recyclerViewStandings.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = <EMAIL>
        }
    }
    
    private fun setupSpinner() {
        leagueSpinnerAdapter = ArrayAdapter(
            requireContext(),
            android.R.layout.simple_spinner_item,
            mutableListOf<String>()
        )
        leagueSpinnerAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerLeagues.adapter = leagueSpinnerAdapter
        
        binding.spinnerLeagues.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                if (leagues.isNotEmpty() && position < leagues.size) {
                    viewModel.loadStandings(leagues[position].id)
                }
            }
            
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }
    }
    
    private fun setupSwipeRefresh() {
        binding.swipeRefresh.setOnRefreshListener {
            val selectedPosition = binding.spinnerLeagues.selectedItemPosition
            if (leagues.isNotEmpty() && selectedPosition < leagues.size) {
                viewModel.loadStandings(leagues[selectedPosition].id)
            }
        }
    }
    
    private fun observeViewModel() {
        viewModel.leagues.observe(viewLifecycleOwner) { leaguesList ->
            leagues.clear()
            leagues.addAll(leaguesList)
            
            val leagueNames = leaguesList.map { getLeagueNameInArabic(it.name) }
            leagueSpinnerAdapter.clear()
            leagueSpinnerAdapter.addAll(leagueNames)
            leagueSpinnerAdapter.notifyDataSetChanged()
            
            if (leaguesList.isNotEmpty()) {
                viewModel.loadStandings(leaguesList[0].id)
            }
        }
        
        viewModel.standings.observe(viewLifecycleOwner) { standings ->
            adapter.submitList(standings)
        }
        
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.swipeRefresh.isRefreshing = isLoading
            binding.progressBar.visibility = if (isLoading && adapter.itemCount == 0) {
                View.VISIBLE
            } else {
                View.GONE
            }
        }
        
        viewModel.error.observe(viewLifecycleOwner) { error ->
            binding.textError.visibility = if (error != null) View.VISIBLE else View.GONE
            if (error != null) {
                binding.textError.text = error
            }
        }
    }
    
    private fun getLeagueNameInArabic(englishName: String): String {
        return when {
            englishName.contains("Premier League", ignoreCase = true) -> "الدوري الإنجليزي الممتاز"
            englishName.contains("La Liga", ignoreCase = true) -> "الدوري الإسباني"
            englishName.contains("Bundesliga", ignoreCase = true) -> "الدوري الألماني"
            englishName.contains("Serie A", ignoreCase = true) -> "الدوري الإيطالي"
            englishName.contains("Ligue 1", ignoreCase = true) -> "الدوري الفرنسي"
            englishName.contains("Champions League", ignoreCase = true) -> "دوري أبطال أوروبا"
            englishName.contains("Europa League", ignoreCase = true) -> "الدوري الأوروبي"
            else -> englishName
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
