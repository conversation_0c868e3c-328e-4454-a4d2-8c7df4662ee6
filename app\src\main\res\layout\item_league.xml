<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/card_margin"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/card_padding">

        <ImageView
            android:id="@+id/image_league_logo"
            android:layout_width="@dimen/league_logo_size"
            android:layout_height="@dimen/league_logo_size"
            android:contentDescription="@string/league_logo_desc"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_menu_leagues" />

        <TextView
            android:id="@+id/text_league_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:textColor="@android:color/black"
            android:textSize="@dimen/text_size_large"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/image_league_logo"
            app:layout_constraintTop_toTopOf="@+id/image_league_logo"
            tools:text="الدوري الإنجليزي الممتاز" />

        <TextView
            android:id="@+id/text_league_season"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:textColor="@color/dark_gray"
            android:textSize="@dimen/text_size_small"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/image_league_logo"
            app:layout_constraintTop_toBottomOf="@+id/text_league_name"
            tools:text="موسم 2023-2024" />

        <TextView
            android:id="@+id/text_league_matchday"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:textColor="@color/dark_gray"
            android:textSize="@dimen/text_size_small"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/image_league_logo"
            app:layout_constraintTop_toBottomOf="@+id/text_league_season"
            tools:text="الجولة 15" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
