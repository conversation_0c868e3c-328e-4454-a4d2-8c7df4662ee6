<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/card_margin"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <TextView
            android:id="@+id/text_position"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="@drawable/circle_background"
            android:gravity="center"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="1" />

        <ImageView
            android:id="@+id/image_team_logo"
            android:layout_width="@dimen/team_logo_size"
            android:layout_height="@dimen/team_logo_size"
            android:layout_marginStart="12dp"
            android:contentDescription="@string/team_logo_desc"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toEndOf="@+id/text_position"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/ic_menu_leagues" />

        <TextView
            android:id="@+id/text_team_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="8dp"
            android:textColor="@android:color/black"
            android:textSize="@dimen/text_size_medium"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/text_points"
            app:layout_constraintStart_toEndOf="@+id/image_team_logo"
            app:layout_constraintTop_toTopOf="@+id/image_team_logo"
            tools:text="مانشستر سيتي" />

        <TextView
            android:id="@+id/text_team_stats"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:textColor="@color/dark_gray"
            android:textSize="@dimen/text_size_small"
            app:layout_constraintEnd_toStartOf="@+id/text_points"
            app:layout_constraintStart_toEndOf="@+id/image_team_logo"
            app:layout_constraintTop_toBottomOf="@+id/text_team_name"
            tools:text="لعب: 15 | فاز: 12 | تعادل: 2 | خسر: 1" />

        <TextView
            android:id="@+id/text_points"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/points_background"
            android:paddingHorizontal="8dp"
            android:paddingVertical="4dp"
            android:textColor="@android:color/white"
            android:textSize="@dimen/text_size_medium"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/image_team_logo"
            tools:text="38" />

        <TextView
            android:id="@+id/text_goal_difference"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="@color/dark_gray"
            android:textSize="@dimen/text_size_small"
            app:layout_constraintEnd_toEndOf="@+id/text_points"
            app:layout_constraintTop_toBottomOf="@+id/text_points"
            tools:text="+25" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
