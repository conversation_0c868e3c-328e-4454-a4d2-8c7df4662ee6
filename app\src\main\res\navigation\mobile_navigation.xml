<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mobile_navigation"
    app:startDestination="@+id/nav_leagues">

    <fragment
        android:id="@+id/nav_leagues"
        android:name="com.europeanleagues.app.ui.leagues.LeaguesFragment"
        android:label="@string/menu_leagues"
        tools:layout="@layout/fragment_leagues" />

    <fragment
        android:id="@+id/nav_standings"
        android:name="com.europeanleagues.app.ui.standings.StandingsFragment"
        android:label="@string/menu_standings"
        tools:layout="@layout/fragment_standings" />

    <fragment
        android:id="@+id/nav_matches"
        android:name="com.europeanleagues.app.ui.matches.MatchesFragment"
        android:label="@string/menu_matches"
        tools:layout="@layout/fragment_matches" />

    <fragment
        android:id="@+id/nav_results"
        android:name="com.europeanleagues.app.ui.results.ResultsFragment"
        android:label="@string/menu_results"
        tools:layout="@layout/fragment_results" />

</navigation>
